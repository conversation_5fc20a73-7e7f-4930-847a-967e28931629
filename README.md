# Agent MCP SNCF

Une solution complète de client Multi-LLM et serveur MCP pour accéder aux données de trains SNCF via les APIs Navitia et SNCF OpenData.

## Vue d'ensemble

Ce projet fournit une solution complète permettant aux LLMs d'accéder aux informations de trains SNCF en temps réel, incluant :

- Un client Multi-LLM qui supporte plusieurs fournisseurs (OpenAI, Anthropic, Google Gemini,...)
- Un serveur MCP qui fournit des outils pour accéder aux données de trains SNCF
- Un exemple d'implémentation multi-agents basé sur Google ADK
- Un évaluateur de performance pour les modèles LLM

## Structure du projet

```
s2025p2-mcp-agent/
├── mcp_client/           # Implémentation du client Multi-LLM
│   ├── streamlit_app/    # Interface Streamlit
│   ├── cli.py           # Interface en ligne de commande
│   ├── app.py           # Application Streamlit principale
│   └── README.md        # Documentation du client
├── mcp_server/          # Implémentation du serveur MCP
│   ├── main.py          # Serveur principal
│   ├── tools/           # Outils MCP pour les données SNCF
│   └── README.md        # Documentation du serveur
├── core_client/         # Client central réutilisable
│   └── src/core_client/ # Code source du client central
├── mcp_evaluator/       # Évaluateur de performance des modèles
├── agent/               # Exemple d'implémentation multi-agents
└── README.md            # Ce fichier
```

## Démarrage rapide

### Prérequis

- Python 3.12 ou supérieur
- `uv` (gestionnaire de paquets Python) **OU** `pip` (gestionnaire Python standard)
- Clé API Navitia
- Clé API SNCF OpenData
- Clés API pour les fournisseurs LLM que vous souhaitez utiliser (OpenAI, Anthropic, etc.)

### Installation

1. Cloner le dépôt et installer les dépendances :

```bash
git clone <url-du-depot>
cd s2025p2-mcp-agent
```

2. Installer `uv` :

```bash
# Via curl
curl -LsSf https://astral.sh/uv/install.sh | sh

# Ou via pip
python -m venv .venv

source .venv/bin/activate  # Linux/Mac
# ou
.venv\Scripts\activate.bat     # Windows

pip install uv
```

### Configuration

1. Configurer les variables d'environnement pour le serveur MCP :
```bash
cd mcp_server
cp .env.example .env
# Éditer .env avec vos clés API
```

2. Configurer les variables d'environnement pour le client :
```bash
cd mcp_client
cp .env.example .env
# Éditer .env avec vos clés API LLM, en utilisant les noms de variables d'environnement appropriés pour LiteLLM
```

3. Configurer la connexion au serveur MCP pour le client
```bash
cd core_client/src/core_client
# Éditer config.json pour pointer vers le serveur MCP. Par défaut, il pointe vers le serveur MCP SNCF.
```

### Lancement du client Multi-LLM

#### Interface en ligne de commande

```bash
cd mcp_client
uv run cli.py --models anthropic/claude-sonnet-4-0 openai/o3
```

#### Interface Streamlit

```bash
cd mcp_client
uv run streamlit run app.py
```

## Fonctionnalités

### Serveur MCP

- Recherche et autocomplétion de gares
- Horaires de départs et d'arrivées
- Planification d'itinéraires avec informations tarifaires
- Recherche d'informations de trains
- Informations de perturbations en temps réel

### Client Multi-LLM

- Support pour plusieurs fournisseurs LLM :
  - OpenAI (modèles GPT)
  - Anthropic (modèles Claude)
  - Google (modèles Gemini via Vertex AI)
  - Autres fournisseurs supportés par LiteLLM (non testés)
- Support d'appel d'outils MCP pour tous les fournisseurs
- Traitement concurrent de requêtes sur plusieurs modèles
- Mode chat interactif avec plusieurs modèles ou un seul modèle
- Messages système configurables avec inclusion automatique de la date/heure
- Basé sur [Google ADK](https://google.github.io/adk-docs/) et [LiteLLM](https://docs.litellm.ai/)

## Documentation détaillée

Pour une documentation plus détaillée, consultez :

- [Documentation du Client MCP](mcp_client/README.md)
- [Documentation du Serveur MCP](mcp_server/README.md)
- [Documentation du Client Central](core_client/README.md)
- [Documentation de l'Évaluateur](mcp_evaluator/README.md)
- [Documentation de l'Agent](agent/README.md)

## Ressources externes utiles

- **Protocole MCP** : [Spécification MCP](https://spec.modelcontextprotocol.io/)
- **Google ADK** : [Documentation complète](https://google.github.io/adk-docs/)
- **LiteLLM** : [Documentation](https://docs.litellm.ai/) et [Configuration des clés API](https://docs.litellm.ai/docs/set_keys)
- **APIs SNCF** : [Navitia](https://doc.navitia.io/) et [SNCF OpenData](https://data.sncf.com/)
