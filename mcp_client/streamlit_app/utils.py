"""
Utility functions for the Multi-LLM Client Streamlit application.

This module provides various helper functions used throughout the application.
"""
import re
import json
import asyncio
import datetime
import os
from typing import Coroutine, Any, Dict, List, Optional, Tuple

# Cache for HTML template to avoid repeated disk reads
_HTML_TEMPLATE_CACHE = None


def run_async(coro: Coroutine) -> Any:
    """
    Run an async coroutine safely.

    Args:
        coro: The coroutine to run

    Returns:
        The result of the coroutine or None if an error occurred
    """
    try:
        return asyncio.run(coro)
    except Exception as e:
        print(f"Error running async function: {e}")
        return None


def replace_timedate_placeholder(text: str) -> str:
    """
    Replace {{TIMEDATE}} and TIMEDATE placeholders with current datetime.

    Args:
        text: The text containing the placeholder

    Returns:
        Text with the placeholder replaced by the current datetime
    """
    if text is None:
        return ""

    current_time = datetime.datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")

    # Replace both formats of the placeholder
    text = text.replace("{{TIMEDATE}}", formatted_time)
    text = re.sub(r'\bTIMEDATE\b', formatted_time, text)

    return text

# Cache for HTML templates
_STATION_SCHEDULE_TEMPLATE_CACHE = None

def _get_html_template() -> str:
    """
    Get the HTML template for journey display, using cache if available.

    Returns:
        HTML template string
    """
    global _HTML_TEMPLATE_CACHE

    if _HTML_TEMPLATE_CACHE is None:
        template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                    'mcp_server', 'templates', 'train_schedule_table.html')
        try:
            with open(template_path, 'r') as f:
                _HTML_TEMPLATE_CACHE = f.read()
        except Exception as e:
            print(f"Error reading template: {e}")
            return "<p>Error reading template: {e}</p>"

    return _HTML_TEMPLATE_CACHE


def _get_station_schedule_html_template() -> str:
    """
    Get the unified HTML template for station schedules (departures/arrivals), using cache if available.

    Returns:
        HTML template string
    """
    global _STATION_SCHEDULE_TEMPLATE_CACHE

    if _STATION_SCHEDULE_TEMPLATE_CACHE is None:
        template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                    'mcp_server', 'templates', 'train_station_schedule_table.html')
        try:
            with open(template_path, 'r') as f:
                _STATION_SCHEDULE_TEMPLATE_CACHE = f.read()
        except Exception as e:
            print(f"Error reading station schedule template: {e}")
            return "<p>Error reading station schedule template: {e}</p>"

    return _STATION_SCHEDULE_TEMPLATE_CACHE


def format_station_schedule_data(schedule_data: Dict, schedule_type: str = "departures") -> str:
    """
    Unified function to format departures or arrivals data into HTML.

    Args:
        schedule_data: The schedule data from get_departures or get_arrivals tool
        schedule_type: "departures" or "arrivals"

    Returns:
        Formatted HTML string for display in Streamlit
    """
    # Determine the data key and labels based on schedule type
    if schedule_type == "arrivals":
        data_key = "arrivals"
        title = "Prochaines arrivées"
        destination_label = "Provenance"
        theme_class = "arrivals-theme"
    else:  # departures
        data_key = "departures"
        title = "Prochains départs"
        destination_label = "Destination"
        theme_class = "departures-theme"

    if not schedule_data or data_key not in schedule_data:
        return f"<p>No {schedule_type} data available</p>"

    # Get the HTML template
    template = _get_station_schedule_html_template()
    if template.startswith("<p>Error"):
        return template

    # Extract schedule information
    schedules = schedule_data.get(data_key, [])
    if not schedules:
        return f"<p>No {schedule_type} found in data</p>"

    # Get station name from the first schedule item
    station_name = "Station"
    if schedules:
        station_name = schedules[0].get("stop_point", {}).get("name", "Station")
        # Clean up station name (remove "Hall 1 & 2" etc.)
        station_name = station_name.replace(" - Hall 1 & 2", "").replace(" (Paris)", "")

    # Replace template placeholders
    template = template.replace('SCHEDULE_THEME_CLASS', theme_class)
    template = template.replace('SCHEDULE_TITLE', title)
    template = template.replace('STATION_NAME', station_name)
    template = template.replace('DESTINATION_ORIGIN_LABEL', destination_label)

    # Generate table rows for all schedules
    table_rows = []
    for schedule in schedules:
        # Extract schedule information
        display_info = schedule.get("display_informations", {})
        stop_date_time = schedule.get("stop_date_time", {})

        # Format time based on schedule type
        if schedule_type == "arrivals":
            time_str = format_datetime(stop_date_time.get("arrival_date_time", ""))
        else:  # departures
            time_str = format_datetime(stop_date_time.get("departure_date_time", ""))

        # Get destination/origin and clean it
        destination_origin = display_info.get("direction", "N/A")

        # For arrivals, extract origin using improved logic
        if schedule_type == "arrivals":
            route_name = schedule.get("route", {}).get("name", "")
            direction_clean = display_info.get("direction", "").split("(")[0].strip()

            # Case 1: High-speed trains with " - " in route name
            if route_name and " - " in route_name:
                parts = route_name.split(" - ")
                if len(parts) >= 2:
                    # Compare with direction to determine which part is the origin
                    left_part = parts[0].strip()
                    right_part = parts[1].strip()

                    # If direction matches the right part, origin is left part
                    if direction_clean in right_part or right_part in direction_clean:
                        destination_origin = left_part
                    # If direction matches the left part, origin is right part
                    elif direction_clean in left_part or left_part in direction_clean:
                        destination_origin = right_part
                    else:
                        # Default: assume left part is origin for arrivals
                        destination_origin = left_part

            # Case 2: TER trains without " - " in route name, use route.direction.name
            else:
                route_direction = schedule.get("route", {}).get("direction", {})
                if isinstance(route_direction, dict) and route_direction.get("name"):
                    route_direction_name = route_direction.get("name", "")
                    if route_direction_name:
                        destination_origin = route_direction_name

        # Clean destination/origin: remove text in parentheses (city names)
        if "(" in destination_origin and ")" in destination_origin:
            destination_origin = destination_origin.split("(")[0].strip()

        # Get train number - use trip_short_name instead of headsign
        train_number = display_info.get("trip_short_name", "N/A")

        # Get train information for intelligent detection
        network = display_info.get("network", "")
        commercial_mode = display_info.get("commercial_mode", "")

        # Use intelligent train type detection
        train_type = detect_train_type(network, commercial_mode, train_number)
        logo_html = get_train_logo(train_type, schedule_type)

        # Add row to table (new column order: Service, N°, Heure, Destination/Provenance)
        table_rows.append(f"""
        <tr class="schedule-row">
            <td class="carrier-column">{logo_html}</td>
            <td class="train-number">{train_number}</td>
            <td>
                <div class="schedule-time">{time_str[0]}</div>
            </td>
            <td>
                <div class="destination-origin">{destination_origin}</div>
            </td>
        </tr>""")

    # Insert the rows into the template
    template = template.replace('</tr>\n    </tbody>', f'{"".join(table_rows)}\n    </tbody>')

    return template


def format_journey_data(journey_data: Dict, selected_tariff: str = "Tarif Normal") -> str:
    """
    Format journey data from the get_journey tool into HTML.

    Args:
        journey_data: The journey data from the get_journey tool
        selected_tariff: The selected tariff type

    Returns:
        Formatted HTML string for display in Streamlit
    """
    if not journey_data or "journeys" not in journey_data:
        return "<p>No journey data available</p>"

    # Get the HTML template
    template = _get_html_template()
    if template.startswith("<p>Error"):
        return template

    # Extract journey information
    journeys = journey_data.get("journeys", [])
    if not journeys:
        return "<p>No journeys found in data</p>"

    # Get from and to stations from the first journey
    from_station, to_station = _extract_stations(journeys[0])

    # Update the journey subtitle
    subtitle = f"{from_station} - {to_station}" if from_station and to_station else "Trajet"
    template = template.replace('<div class="journey-subtitle">Paris - Marseille</div>',
                               f'<div class="journey-subtitle">{subtitle}</div>')

    # Generate table rows for all journeys
    table_rows = []
    for journey in journeys:
        # Format journey data
        departure_time = format_datetime(journey.get("departure", ""))
        arrival_time = format_datetime(journey.get("arrival", ""))
        duration = format_duration(journey.get("duration", 0))
        transfer_badge = get_transfer_badge(journey.get("nb_transfers", 0))
        carrier_info, price_info = get_carrier_and_price(journey, selected_tariff)

        # Add row to table
        table_rows.append(f"""
        <tr class="journey-row">
            <td>
                <div class="journey-time departure">{departure_time[0]}</div>
                <div class="journey-station">{from_station}</div>
            </td>
            <td>
                <div class="journey-time arrival">{arrival_time[0]}</div>
                <div class="journey-station">{to_station}</div>
            </td>
            <td class="duration">{duration}</td>
            <td class="correspondence-column">{transfer_badge}</td>
            <td class="carrier-column">{carrier_info}</td>
            <td class="price-column">
                <div class="price-range">{price_info}</div>
            </td>
        </tr>""")

    # Insert the rows into the template
    template = template.replace('</tr>\n    </tbody>', f'{"".join(table_rows)}\n    </tbody>')

    return template


def _extract_stations(journey: Dict) -> Tuple[str, str]:
    """
    Extract from and to stations from a journey.

    Args:
        journey: Journey data

    Returns:
        Tuple of (from_station, to_station)
    """
    from_station = ""
    to_station = ""

    if "sections" in journey:
        for section in journey["sections"]:
            if section.get("from") and not from_station:
                from_station = section.get("from")
            if section.get("to") and not to_station:
                to_station = section.get("to")

    return from_station, to_station


def format_datetime(datetime_str: str) -> tuple:
    """
    Format a datetime string from the API into a human-readable format.

    Args:
        datetime_str: Datetime string in format YYYYMMDDThhmmss

    Returns:
        Tuple of (time, date) strings
    """
    if not datetime_str or len(datetime_str) < 15:
        return ("--:--", "")

    try:
        # Extract components using slicing (faster than regex)
        year, month, day = datetime_str[0:4], datetime_str[4:6], datetime_str[6:8]
        hour, minute = datetime_str[9:11], datetime_str[11:13]

        # Format time and date
        return (f"{hour}:{minute}", f"{day}/{month}/{year}")
    except Exception:
        return ("--:--", "")


def format_duration(seconds: int) -> str:
    """
    Format duration in seconds to hours and minutes.

    Args:
        seconds: Duration in seconds

    Returns:
        Formatted duration string
    """
    if not seconds:
        return "--:--"

    hours, minutes = divmod(seconds, 3600)
    minutes //= 60

    return f"{hours}h {minutes:02d}min" if hours > 0 else f"{minutes}min"


def get_transfer_badge(transfers: int) -> str:
    """
    Get HTML for transfer badge.

    Args:
        transfers: Number of transfers

    Returns:
        HTML string for transfer badge
    """
    # Use a simple ternary operator for clarity and performance
    return '<span class="direct-badge">Direct</span>' if transfers == 0 else f'<span class="connection-badge">{transfers}</span>'


# Cache for train logos to avoid recreating them
_TRAIN_LOGOS = {
    "ouigo": """<svg class="train-logo" viewBox="0 0 230 230" width="100" height="40">
        <g transform="matrix(1.25 0 0 -1.25 -249.606 344.236)">
            <path d="M0 0c-6.325 0-10.821 5.154-10.821 11.29 0 6.126 4.391 11.181 10.725 11.181 6.339 0 10.825-5.164 10.825-11.299C10.729 5.06 6.337 0 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(292.745 251.588)"/>
            <path d="M0 0c-6.325 0-10.821 5.147-10.821 11.284 0 6.123 4.391 11.172 10.725 11.172 6.339 0 10.825-5.145 10.825-11.292C10.729 5.053 6.337 0 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(292.745 91.71)"/>
            <path d="M0 0c0-50.582-40.979-91.554-91.548-91.554-50.59 0-91.563 40.972-91.563 91.554 0 50.518 40.973 91.539 91.563 91.539C-40.979 91.539 0 50.518 0 0" style="fill:#e3006a;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(383.596 183.05)"/>
            <path d="M0 0v16.799a3.902 3.902 0 0 0 3.932 3.934 3.913 3.913 0 0 0 3.936-3.934V.214c0-5.83 2.906-8.839 7.705-8.839 4.801 0 7.711 2.904 7.711 8.559v16.865a3.904 3.904 0 0 0 3.928 3.934 3.903 3.903 0 0 0 3.932-3.934V.248c0-10.821-6.07-16.123-15.676-16.123C5.879-15.875 0-10.528 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(250.506 179.15)"/>
            <path d="M0 0a3.91 3.91 0 0 0 3.938 3.938A3.906 3.906 0 0 0 7.874 0v-28.49c0-2.2-1.738-3.938-3.936-3.938A3.91 3.91 0 0 0 0-28.49z" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(286.956 195.944)"/>
            <path d="M0 0a4.773 4.773 0 0 0-4.771-4.771A4.768 4.768 0 0 0-9.535 0 4.768 4.768 0 0 0 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(295.663 209.258)"/>
            <path d="M0 0h-9.197a3.426 3.426 0 0 1-3.418-3.429c0-1.892 1.528-3.367 3.418-3.367h5.747v-4.89c-1.817-1.685-4.401-2.363-8.065-2.363-6.322 0-11.123 4.852-11.123 11.38 0 6.085 4.85 11.194 10.567 11.194 3.375 0 5.667-.923 7.82-2.451.561-.406 1.267-.809 2.395-.809a3.878 3.878 0 0 1 3.885 3.878c0 1.522-.873 2.601-1.633 3.162-3.223 2.251-6.8 3.471-12.206 3.471-10.828 0-19.053-8.379-19.053-18.541 0-10.563 7.97-18.433 19.108-18.433 5.531 0 10.201 1.687 13.591 4.326 1.743 1.335 2.106 2.391 2.106 4.696v8.251A3.912 3.912 0 0 1 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(330.636 184.419)"/>
            <path d="M0 0c-6.333 0-10.819 5.154-10.819 11.288 0 6.128 4.392 11.183 10.725 11.183 6.335 0 10.826-5.154 10.826-11.29C10.732 5.062 6.331 0 0 0m0 29.714c-11.027 0-19.047-8.371-19.047-18.533 0-10.16 7.919-18.43 18.953-18.43 11.02 0 19.04 8.373 19.04 18.537 0 10.16-7.911 18.426-18.946 18.426" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(355.993 170.465)"/>
            <path d="M0 0c-6.34 0-10.826 5.154-10.826 11.288 0 6.128 4.388 11.183 10.725 11.183 6.331 0 10.822-5.154 10.822-11.29C10.721 5.062 6.325 0 0 0m0 29.714c-11.033 0-19.055-8.371-19.055-18.533 0-10.16 7.917-18.43 18.954-18.43 11.027 0 19.041 8.373 19.041 18.537 0 10.16-7.915 18.426-18.94 18.426" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(228.19 170.465)"/>
        </g>
    </svg>""",

    "tgv_inoui": """<svg class="train-logo" viewBox="0 0 700 314" width="100" height="40">
        <path d="M64.92 787.128h-2.356v.922H68.3v-.922h-2.373v-6.436H64.92zm10.74-2.325v-3.632c-.194-.111-.397-.193-.619-.286-.249-.082-.47-.167-.73-.223-.248-.055-.498-.081-.747-.107-.259-.03-.48-.056-.702-.056-.647 0-1.2.108-1.681.304-.471.23-.896.505-1.228.84a3.55 3.55 0 0 0-.785 1.263c-.167.442-.25.947-.25 1.481 0 .528.083 1.033.277 1.512.194.472.444.895.785 1.23.333.356.758.616 1.229.835.452.197.979.279 1.542.279.665 0 1.228-.082 1.672-.25.425-.166.785-.385 1.07-.701l-.7-.754c-.306.286-.62.48-.98.59a2.828 2.828 0 0 1-1.062.194c-.425 0-.813-.086-1.145-.25a2.31 2.31 0 0 1-.868-.62 2.354 2.354 0 0 1-.564-.947c-.138-.334-.194-.728-.194-1.118 0-.423.056-.81.194-1.177.166-.36.36-.676.62-.925.248-.275.553-.468.895-.616.36-.167.72-.223 1.145-.223.36 0 .702.03 1.007.085.305.082.582.164.812.286v2.065H73v.921zm.813 3.247h1.145l2.152-5.984h.028l2.235 5.984h1.126l-2.909-7.358h-.95l-2.826 7.358zm-4.563-17.372a3.56 3.56 0 0 0 3.556 3.55c1.958 0 3.574-1.595 3.574-3.55v-10.575h4.12v10.575c0 4.223-3.446 7.689-7.694 7.689-4.23 0-7.693-3.466-7.693-7.689v-10.575h4.137zm-9.124 6.85A39.22 39.22 0 0 1 61.779 769v-5.735c.111.083.25.166.37.25.526.276 1.117.479 1.755.479.757 0 1.459-.258 2.05-.673v14.207z" style="fill:#63675c;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="matrix(9.3048 0 0 -9.3048 -573.296 7335.297)"/>
        <path d="M63.904 762.399a2.29 2.29 0 0 1-2.291-2.296 2.267 2.267 0 0 1 2.29-2.296c1.266 0 2.3 1.005 2.3 2.296a2.31 2.31 0 0 1-2.3 2.296zm62.616 4.554a3.56 3.56 0 0 0-3.557-3.55 3.552 3.552 0 0 0-3.546 3.55v10.575h-4.148v-10.575c0-4.222 3.475-7.69 7.694-7.69 4.26 0 7.693 3.468 7.693 7.69v10.575h-4.137zm8.005 8.28c1.284 0 2.318 1.042 2.318 2.295 0 1.292-1.034 2.295-2.318 2.295a2.285 2.285 0 0 1-2.299-2.295 2.31 2.31 0 0 1 2.3-2.295zm1.12-15.13c.636 2.738 1.005 5.597 1.005 8.529v5.734c-.108-.083-.25-.166-.369-.248-.524-.277-1.116-.471-1.756-.471a3.4 3.4 0 0 0-2.02.664v-14.208zM99.22 778.367c5.253 0 9.539-4.277 9.539-9.56 0-5.265-4.286-9.543-9.54-9.543-5.263 0-9.55 4.278-9.55 9.543 0 5.283 4.287 9.56 9.55 9.56zm0-23.778c7.86 0 14.23 6.38 14.23 14.218 0 7.855-6.37 14.236-14.23 14.236-7.843 0-14.215-6.38-14.215-14.236 0-7.837 6.372-14.218 14.215-14.218" style="fill:#930c38;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="matrix(9.3048 0 0 -9.3048 -573.296 7335.297)"/>
    </svg>""",

    "eurostar": """<svg class="train-logo" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="100" height="40" viewBox="0 0 146 40"><path fill="#fbdd10" d="m43.319 39.121.11-11.921 7.92.073-.015 1.566-6.086-.057-.03 3.272 5.333.05-.014 1.6-5.334-.05-.036 3.914 6.435.06-.014 1.57-8.27-.077zm11.427-4.545.067-7.27 1.83.016-.064 7.083c-.019 1.948.802 3.444 3.385 3.468 2.486.023 3.226-1.355 3.245-3.406l.065-7.084 1.834.017-.067 7.27c-.025 2.787-1.598 4.826-5.279 4.793-3.6-.033-5.04-2.29-5.016-4.887m13.802 4.778.11-11.921 3.392.031c3.23.03 4.96 1.165 4.939 3.506-.014 1.407-.85 2.589-2.405 3.22l3.626 5.061v.192l-2.026-.019-3.19-4.508-2.57-.024-.042 4.479zm5.02-6.017c.832-.438 1.566-1.097 1.577-2.311.013-1.484-.803-1.976-3.11-1.997l-1.557-.014-.04 4.293zm5.965.156c.029-3.167 2.18-6.132 6.231-6.094 4.035.037 6.127 3.04 6.098 6.207-.03 3.177-2.176 6.133-6.21 6.096-4.052-.038-6.149-3.033-6.12-6.209zm10.397.096c.024-2.599-1.418-4.568-4.18-4.593-2.783-.026-4.261 1.916-4.286 4.514-.023 2.6 1.42 4.575 4.202 4.6 2.762.026 4.24-1.923 4.264-4.52zm4.075 5.448.015-1.694.187.002c1.102.495 2.488.867 3.667.878 2.12.02 3.143-.624 3.153-1.748.01-1.112-.857-1.625-2.105-2.06l-1.927-.664c-1.706-.61-2.548-1.626-2.535-3.099.018-2.053 1.963-3.155 4.717-3.13 1.11.01 2.755.314 3.51.695l-.016 1.579-.19-.002c-1.082-.413-2.455-.666-3.319-.674-1.787-.017-2.858.324-2.869 1.45-.01 1.028.792 1.367 1.73 1.691l1.836.68c1.74.618 3.03 1.51 3.011 3.47-.02 2.138-1.968 3.431-5.01 3.403-1.461-.014-2.758-.301-3.855-.777m15.007.69.094-10.356-4.22-.04.014-1.564 10.21.093-.015 1.566-4.153-.038-.095 10.355zm6.056.055.002-.191 4.704-11.687 2.307.021 4.2 11.77-.003.19-1.923-.017-1.103-3.188-5.21-.049-1.237 3.167zm7.637-4.673-1.933-5.528-2.128 5.491zm6.31 4.802.11-11.92 3.395.03c3.236.03 4.957 1.165 4.936 3.506-.013 1.407-.845 2.59-2.41 3.22l3.64 5.062-.001.191-2.035-.019-3.188-4.508-2.563-.024-.041 4.479zm5.03-6.017c.827-.438 1.56-1.097 1.571-2.31.014-1.485-.798-1.976-3.11-1.998l-1.553-.014-.04 4.293zm6.693-2.815.022-2.476-.826-.008.005-.521 2.307.021-.005.521-.821-.007-.023 2.476zm4.575.042.018-2.002-.781 1.224-.328-.003-.747-1.213-.018 1.976-.625-.005.027-2.997.728.006.885 1.426.898-1.408.63.005-.028 2.997-.66-.006z" style="fill-rule:evenodd"/><path fill="#fff" d="M41.866 12.208c.73-.933 1.35-2.006 1.792-3.059.11-.26.152-.556.2-.835a5.98 5.98 0 0 0 .08-.945c.039-4.022-3.833-7.317-8.645-7.36l-.936-.008C28.08-.056 23.715 3.796 21.301 6.13c-5.034 4.87-8.369 11.19-9.293 18.22-1.23.457-2.468.888-3.723 1.275-1.13.348-2.272.66-3.43.907a21.661 21.661 0 0 1-3.054.45c-.497.034-.99.043-1.487.005-.224-.017-.313.466-.314.607-.008.802.923 1.96 3.506 1.984 2.047.018 5.036-.614 8.258-1.523v-.301c-.033 3.538.764 6.738 2.289 8.67.99 1.254 2.93 1.768 4.467 1.922 1.984.2 4.034-.173 5.95-.808 4.07-1.347 7.748-3.528 11.179-6.033.47-.344.926-.706 1.398-1.05.325-.235.641-.501.96-.75.378-.293.965-.801 1.316-1.125a3.77 3.77 0 0 0 .42-.44 1.21 1.21 0 0 0 .235-.799c-.006-.118-.006-.27-.067-.376a.247.247 0 0 0-.096-.092c-.078-.045-.125-.005-.202.032-.327.156-.647.323-.966.496-.35.19-.71.362-1.071.532-.336.158-.658.342-.996.493-.618.275-1.219.574-1.828.863a27.97 27.97 0 0 1-.916.418c-2.063.89-4.125 1.794-6.266 2.489-2.202.714-5.64 1.654-7.105-.87-.565-.974-.574-2.187-.564-3.277.008-.906.075-1.807.19-2.7a157.675 157.675 0 0 0 4.073-1.544 615.485 615.485 0 0 1 7.447-2.885c1.368-.52 2.689-1.08 4.085-1.524l3.628-1.153c1.353-.43 2.707-.765 4.107-.961 2.904-.406 5.752-.331 8.648.102 1.3.194 2.589.451 3.867.75 2.476.58 4.907 1.33 7.334 2.084 3.882 1.204 7.655 1.803 11.684 2.192a28.78 28.78 0 0 0 1.973.122c3.562.102 7.144-.405 10.553-1.388a31.645 31.645 0 0 0 6.222-2.556c.546-.295 1.08-.611 1.59-.954a23.734 23.734 0 0 0 2.29-1.734 19.43 19.43 0 0 0 1.056-.973c.29-.29.666-.584.785-.985.073-.242.034-.439-.029-.68-.028-.112-.106-.208-.217-.16-.381.162-.74.383-1.113.567-.773.382-1.56.737-2.359 1.063-1.71.696-3.484 1.254-5.26 1.588-4.996.937-10.4 1.258-15.442.58-5.048-.68-9.923-2.201-14.91-3.178a88.819 88.819 0 0 0-4.64-.795 52.275 52.275 0 0 0-4.74-.467 36.666 36.666 0 0 0-4.75.057c-1.554.121-3.09.39-4.641.54-.033.003-.072.004-.095-.02-.032-.038.003-.094.037-.131.204-.222.401-.455.592-.698m-5.493.761c-2.933 2.699-5.356 3.43-9.01 5.043-2.142.92-4.327 1.867-6.505 2.788 1.204-3.633 3.072-7.074 5.343-10.22 1.16-1.607 2.156-2.475 3.734-3.754 1.968-1.593 5.402-3.152 7.528-.897.893.948 1.176 2.27.987 3.522-.239 1.582-.882 2.42-2.077 3.518" style="fill-rule:evenodd;fill:#06183d;fill-opacity:1"/></svg>""",

    "tgv": """<svg class="train-logo" viewBox="0 0 100 40" width="100" height="40">
        <path d="M20,10h60v20H20V10z" fill="#0088ce"/>
        <text x="50" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle">TGV</text>
    </svg>""",

    "ter": """<svg class="train-logo" viewBox="0 0 100 40" width="100" height="40">
        <path d="M20,10h60v20H20V10z" fill="#00a650"/>
        <text x="50" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle">TER</text>
    </svg>""",

    "ter_departures": """<svg class="train-logo" viewBox="0 0 100 40" width="100" height="40">
        <path d="M20,10h60v20H20V10z" fill="#1976d2"/>
        <text x="50" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle">TER</text>
    </svg>""",

    "ter_arrivals": """<svg class="train-logo" viewBox="0 0 100 40" width="100" height="40">
        <path d="M20,10h60v20H20V10z" fill="#00a650"/>
        <text x="50" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle">TER</text>
    </svg>""",

    "default": """<svg class="train-logo" viewBox="0 0 100 40" width="100" height="40">
        <path d="M20,10h60v20H20V10z" fill="#0088ce"/>
        <text x="50" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle">TRAIN</text>
    </svg>"""
}


def get_carrier_and_price(journey: Dict, selected_tariff: str = "Tarif Normal") -> Tuple[str, str]:
    """
    Extract carrier and price information from journey.

    Args:
        journey: Journey data
        selected_tariff: The selected tariff type

    Returns:
        Tuple of (carrier_html, price_html)
    """
    carrier = ""
    price = "N/A"

    # Find the first public transport section with carrier info
    for section in journey.get("sections", []):
        if "transport_info" in section:
            transport_info = section["transport_info"]

            # Get train information for intelligent detection
            network = transport_info.get("network", "")
            commercial_mode = transport_info.get("commercial_mode", "")
            trip_short_name = transport_info.get("trip_short_name", "")

            # Use intelligent train type detection
            train_type = detect_train_type(network, commercial_mode, trip_short_name)
            carrier = _TRAIN_LOGOS[train_type]

            # Extract price information for the selected tariff
            price = _extract_price_info(transport_info.get("prices", ""), selected_tariff)
            break

    return (carrier, price)


def _extract_price_info(price_data, selected_tariff: str = "Tarif Normal") -> str:
    """
    Extract price information from various formats for a specific tariff.
    Falls back to "Tarif Normal" if the selected tariff is not available.

    Args:
        price_data: Price data from transport_info
        selected_tariff: The selected tariff type

    Returns:
        Formatted price string for the selected tariff, or "Tarif Normal" as fallback, or "N/A" if no prices available
    """
    # Default price
    if not price_data:
        return "N/A"

    # Helper functions for cleaner code
    def clean_unicode(text: str) -> str:
        """Clean Unicode escape sequences."""
        return text.replace("\\u20ac", "€").replace("\\u00e8", "è").replace("\\u00e9", "é").replace("\\u00e0", "à")

    def format_price_range(price_str: str) -> str:
        """Extract and format price range from string."""
        cleaned = clean_unicode(price_str)
        match = re.search(r'(\d+\.\d+)€?\s*-\s*(\d+\.\d+)€?', cleaned)
        if match:
            min_p, max_p = int(float(match.group(1))), int(float(match.group(2)))
            return f"{min_p}€ - {max_p}€"
        return price_str

    def find_tariff(tariff: str, data: dict) -> str:
        """Find tariff price in dictionary with Unicode handling."""
        # Direct match
        if tariff in data:
            return format_price_range(data[tariff])
        # Unicode decoded match
        for key, value in data.items():
            if clean_unicode(key) == tariff:
                return format_price_range(value)
        return None

    # Main logic - simplified
    if isinstance(price_data, dict):
        # Try selected tariff
        result = find_tariff(selected_tariff, price_data)
        if result:
            return result
        # Fallback to "Tarif Normal" if different
        if selected_tariff != "Tarif Normal":
            fallback = find_tariff("Tarif Normal", price_data)
            if fallback:
                return fallback
        # Legacy format
        if "min_price" in price_data and "max_price" in price_data:
            return f"{price_data['min_price']}€ - {price_data['max_price']}€"

    elif isinstance(price_data, str):
        # Parse string format
        tariffs = {}
        for part in price_data.split(", "):
            if ":" in part:
                name, price = part.split(":", 1)
                tariffs[name.strip()] = price.strip()

        # Try selected, then fallback to Normal
        for tariff in [selected_tariff, "Tarif Normal"]:
            if tariff in tariffs:
                return format_price_range(tariffs[tariff])
            if tariff == selected_tariff:  # Only try fallback if first attempt failed
                continue
            break

    return "N/A"


def detect_train_type(network: str, commercial_mode: str = "", trip_short_name: str = "") -> str:
    """Detect train type from network, commercial mode, and trip name."""
    # Combine all text for pattern matching
    text = f"{network} {commercial_mode} {trip_short_name}".lower()

    # Check patterns in priority order
    patterns = [
        ("ouigo", "ouigo"),
        ("eurostar", "eurostar"),
        ("inoui", "tgv_inoui"),
        ("tgv", "tgv"),
        ("ter", "ter")
    ]

    for pattern, train_type in patterns:
        if pattern in text:
            return train_type

    return "default"


def get_train_logo(train_type: str, schedule_type: str = None) -> str:
    """
    Get SVG logo for train type.

    Args:
        train_type: Type of train
        schedule_type: Optional schedule type ("departures" or "arrivals") for TER color variation

    Returns:
        SVG HTML for the train logo
    """
    train_type_lower = train_type.lower()

    # Special handling for TER trains based on schedule type
    if train_type_lower == "ter" and schedule_type:
        if schedule_type == "departures":
            return _TRAIN_LOGOS["ter_departures"]
        elif schedule_type == "arrivals":
            return _TRAIN_LOGOS["ter_arrivals"]

    return _TRAIN_LOGOS.get(train_type_lower, _TRAIN_LOGOS["default"])


def _try_extract_json(text: str, key: str = "journeys") -> Optional[Dict]:
    """Extract JSON data containing specified key from text."""
    # Direct JSON parsing
    try:
        parsed = json.loads(text)
        if isinstance(parsed, dict) and key in parsed:
            return parsed
    except (json.JSONDecodeError, TypeError):
        pass

    # Regex extraction for embedded JSON
    try:
        pattern = rf'(\{{.*"{key}":\s*\[.*\].*\}})'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            parsed = json.loads(match.group(1))
            if key in parsed:
                return parsed
    except Exception:
        pass

    return None


def extract_all_schedule_data_from_tool_calls(tool_calls: List[Dict], schedule_type: str) -> List[Dict]:
    """
    Extract ALL schedule data (departures or arrivals) from tool calls.

    Args:
        tool_calls: List of tool calls from the model response
        schedule_type: "departures" or "arrivals"

    Returns:
        List of schedule data dictionaries
    """
    if not tool_calls:
        return []

    # Log tool calls for debugging
    tool_names = [tool_call.get("tool_name", "unknown") for tool_call in tool_calls]
    print(f"Found {len(tool_calls)} tool calls: {', '.join(tool_names)}")

    schedule_data_list = []
    expected_tool_name = f"get_{schedule_type}"

    # Check ALL schedule-related tool calls
    for tool_call in tool_calls:
        tool_name = tool_call.get("tool_name", "").lower()
        if tool_name == expected_tool_name and tool_call.get("result"):
            result = tool_call["result"]
            schedule_data = None

            # Case 1: Result is already a dictionary with schedule data
            if isinstance(result, dict) and schedule_type in result:
                schedule_data = result

            # Case 2: Result is a string - try to extract JSON
            elif isinstance(result, str):
                schedule_data = _try_extract_json(result, schedule_type)

            # Case 3: Result has a nested 'result' field
            elif isinstance(result, dict) and "result" in result:
                nested_result = result["result"]

                # Try string parsing
                if isinstance(nested_result, str):
                    schedule_data = _try_extract_json(nested_result, schedule_type)

                # Try content attribute in nested result
                elif hasattr(nested_result, 'content') and nested_result.content:
                    for content_item in nested_result.content:
                        if hasattr(content_item, 'text') and content_item.text:
                            schedule_data = _try_extract_json(content_item.text, schedule_type)
                            if schedule_data:
                                break

            # Add to list if we found valid data
            if schedule_data:
                schedule_data_list.append(schedule_data)

    return schedule_data_list


def extract_schedule_data_from_tool_calls(tool_calls: List[Dict], schedule_type: str) -> Optional[Dict]:
    """
    Extract first schedule data (departures or arrivals) from tool calls.
    Kept for backward compatibility.
    """
    all_data = extract_all_schedule_data_from_tool_calls(tool_calls, schedule_type)
    return all_data[0] if all_data else None


def extract_all_journey_data_from_tool_calls(tool_calls: List[Dict]) -> List[Dict]:
    """
    Extract ALL journey data from tool calls.

    Args:
        tool_calls: List of tool calls from the model response

    Returns:
        List of journey data dictionaries
    """
    if not tool_calls:
        return []

    # Log tool calls for debugging
    tool_names = [tool_call.get("tool_name", "unknown") for tool_call in tool_calls]
    print(f"Found {len(tool_calls)} tool calls: {', '.join(tool_names)}")

    journey_data_list = []

    # Check ALL journey-related tool calls
    for tool_call in tool_calls:
        tool_name = tool_call.get("tool_name", "").lower()
        if (tool_name == "get_journeys" or tool_name == "get_journey") and tool_call.get("result"):
            result = tool_call["result"]
            journey_data = None

            # Case 1: Result is already a dictionary with journeys
            if isinstance(result, dict) and "journeys" in result:
                journey_data = result

            # Case 2: Result is a string - try to extract JSON
            elif isinstance(result, str):
                journey_data = _try_extract_json(result)

            # Case 3: Result has a 'content' attribute (ADK format)
            elif hasattr(result, 'content') and result.content:
                for content_item in result.content:
                    if hasattr(content_item, 'text') and content_item.text:
                        journey_data = _try_extract_json(content_item.text)
                        if journey_data:
                            break

            # Case 4: Result has a nested 'result' field
            elif isinstance(result, dict) and "result" in result:
                nested_result = result["result"]

                # Try string parsing
                if isinstance(nested_result, str):
                    journey_data = _try_extract_json(nested_result)

                # Try content attribute in nested result
                elif hasattr(nested_result, 'content') and nested_result.content:
                    for content_item in nested_result.content:
                        if hasattr(content_item, 'text') and content_item.text:
                            journey_data = _try_extract_json(content_item.text)
                            if journey_data:
                                break

            # Add to list if we found valid data
            if journey_data:
                journey_data_list.append(journey_data)

    return journey_data_list


def extract_journey_data_from_tool_calls(tool_calls: List[Dict]) -> Optional[Dict]:
    """
    Extract first journey data from tool calls.
    Kept for backward compatibility.
    """
    all_data = extract_all_journey_data_from_tool_calls(tool_calls)
    return all_data[0] if all_data else None


def generate_multiple_html_templates(tool_calls: List[Dict], selected_tariff: str = "Tarif Normal") -> str:
    """
    Generate multiple HTML templates for all tool calls that require HTML display.

    Args:
        tool_calls: List of tool calls from the model response
        selected_tariff: The selected tariff type

    Returns:
        Combined HTML string with all templates
    """
    html_sections = []

    # Extract all journey data
    journey_data_list = extract_all_journey_data_from_tool_calls(tool_calls)
    for i, journey_data in enumerate(journey_data_list):
        # Generate title for multiple journey results
        if len(journey_data_list) > 1:
            title = f"<h3 style='color: #0066CC; margin: 20px 0 10px 0;'>Résultats de voyage {i+1}</h3>"
            html_sections.append(title)

        # Generate journey HTML
        journey_html = format_journey_data(journey_data, selected_tariff)
        html_sections.append(journey_html)

    # Extract all departures data
    departures_data_list = extract_all_schedule_data_from_tool_calls(tool_calls, "departures")
    for i, departures_data in enumerate(departures_data_list):
        # Generate title for multiple departures results
        if len(departures_data_list) > 1:
            title = f"<h3 style='color: #0066CC; margin: 20px 0 10px 0;'>Départs {i+1}</h3>"
            html_sections.append(title)

        # Generate departures HTML
        departures_html = format_station_schedule_data(departures_data, "departures")
        html_sections.append(departures_html)

    # Extract all arrivals data
    arrivals_data_list = extract_all_schedule_data_from_tool_calls(tool_calls, "arrivals")
    for i, arrivals_data in enumerate(arrivals_data_list):
        # Generate title for multiple arrivals results
        if len(arrivals_data_list) > 1:
            title = f"<h3 style='color: #0066CC; margin: 20px 0 10px 0;'>Arrivées {i+1}</h3>"
            html_sections.append(title)

        # Generate arrivals HTML
        arrivals_html = format_station_schedule_data(arrivals_data, "arrivals")
        html_sections.append(arrivals_html)

    # Combine all HTML sections
    if html_sections:
        return "\n".join(html_sections)

    return None



