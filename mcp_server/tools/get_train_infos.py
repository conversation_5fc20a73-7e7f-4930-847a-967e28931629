"""
Tool for getting train information.
"""

from typing import Any, Dict, List, Optional
import os
from mcp.types import Tool

from tools import SncfTools

from utils.navitia import make_navitia_request, NAVITIA_BASE_URL

async def get_train_infos(
    train_number: str,
    # count: int = 2,
    data_freshness: str = "realtime",
    since: str | None = None,
    until: str | None = None,
) -> Any:
    """
    Get train information and any disruptions that may affect the train.

    Args:
        train_number: Train number (headsign)
        count: Number of results to return
        data_freshness: Data freshness level (realtime, base_schedule, adapted_schedule)
        since: Optional start time (format: YYYYMMDDThhmmss)
        until: Optional end time (format: YYYYMMDDThhmmss)

    Returns:
        Dictionary with train information and disruptions
    """
    count = 2
    request = f"{NAVITIA_BASE_URL}/coverage/sncf/vehicle_journeys?headsign={train_number}&count={count}&data_freshness={data_freshness}{'&since=' + since if since else ''}{'&until=' + until if until else ''}"
    response = await make_navitia_request(request)
    if response is None:
        return "Error: No response from Navitia API"

    result = {
        "train_infos": response["vehicle_journeys"],
        "disruptions": []
    }

    for disruption in response["disruptions"]:
        simplified_disruption = {
            "id": disruption["id"],
            "application_periods": disruption["application_periods"],
            "impacted_objects": disruption["impacted_objects"]["pt_object"]
        }

        if "messages" in disruption:
            simplified_disruption["messages"] = disruption["messages"]["text"]

        result["disruptions"].append(simplified_disruption)

    return result


def get_tool_definition() -> Tool:
    """
    Get the Tool definition for the get_train_infos tool.

    Returns:
        Tool: The Tool definition
    """
    return Tool(
        name=SncfTools.GET_TRAIN_INFOS.value,
        description="Get train informations",
        inputSchema={
            "type": "object",
            "properties": {
                "train_number": {
                    "type": "string",
                    "description": "Train number (headsign)"
                },
                "data_freshness": {
                    "type": "string",
                    "description": "Data freshness (realtime, base_schedule or adapted_schedule). Adapted schedule to get disruptions information.",
                    "default": "realtime"
                },
                "since": {
                    "type": "string",
                    "description": "Optional since time (format: YYYYMMDDThhmmss)",
                    "default": None
                },
                "until": {
                    "type": "string",
                    "description": "Optional until time (format: YYYYMMDDThhmmss)",
                    "default": None
                }
            },
            "required": ["train_number"]
        },
    )
