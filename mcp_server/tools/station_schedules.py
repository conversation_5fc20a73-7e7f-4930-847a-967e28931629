"""
Tool for getting departure and arrival information from/to a station.
"""

from typing import Any, Dict, List, Optional, Literal
from mcp.types import Tool

from tools import SncfTools

from utils.navitia import make_navitia_request
from tools.autocomplete_places import autocomplete_places

async def get_station_schedules(
    station: str,
    schedule_type: Literal["departures", "arrivals"],
    datetime: str | None = None,
    data_freshness: str = "realtime",
    count: int = 10
) -> Any:
    """
    Get departures or arrivals from/to a station, and the disruptions that may affect them.

    Args:
        station: Station name or code
        schedule_type: Type of schedule to retrieve ("departures" or "arrivals")
        count: Number of schedules to return
        datetime: Optional time (format: YYYYMMDDThhmmss)
        data_freshness: Data freshness level (realtime, base_schedule, adapted_schedule)

    Returns:
        Dictionary with schedules and disruptions
    """
    # Check if already stop_area or stop_point
    if station.startswith("stop_area:") or station.startswith("stop_point:"):
        place = station
    else:
        place = await autocomplete_places(query=station, searched_type="stop_area", count=1)
        if place is None:
            return f"Error: No place found"
        place = place[0]["id"]

    # Make the API request
    schedules = await make_navitia_request(
        f"{place.split(':')[0]}s/{place}/{schedule_type}?count={count}"
        f"&forbidden_uris%5B%5D=physical_mode%3ARapidTransit"
        f"&forbidden_uris%5B%5D=physical_mode%3ABus"
        f"&forbidden_uris%5B%5D=physical_mode%3ACoach"
        f"&forbidden_uris%5B%5D=physical_mode%3ATramway"
        f"{'&from_datetime=' + datetime if datetime else ''}"
        f"&data_freshness={data_freshness}"
    )

    if schedules is None:
        return f"Error: No {schedule_type} found"

    # Filter and simplify the schedules for LLM consumption
    result = {
        schedule_type: [],
        "disruptions": []
    }

    # The key in the response depends on the schedule_type
    schedule_items = schedules[schedule_type]

    for item in schedule_items:
        simplified_item = {
            "display_informations": {
                "direction": item["display_informations"].get("direction", ""),
                "physical_mode": item["display_informations"].get("physical_mode", ""),
                "trip_short_name": item["display_informations"].get("trip_short_name", ""),
                "network": item["display_informations"].get("network", ""),
                "code": item["display_informations"].get("code", "")
            },
            "stop_point": {
                "name": item["stop_point"].get("name", ""),
                "physical_modes": item["stop_point"].get("physical_modes", []),
                "coord": item["stop_point"].get("coord", {}),
                "label": item["stop_point"].get("label", ""),
                "id": item["stop_point"].get("id", "")
            },
            "route": {
                "id": item["route"].get("id", ""),
                "name": item["route"].get("name", ""),
                "direction": {
                    "name": item["route"].get("direction", {}).get("name", "") if item["route"].get("direction") else ""
                }
            },
            "stop_date_time": {
                "arrival_date_time": item["stop_date_time"].get("arrival_date_time", ""),
                "departure_date_time": item["stop_date_time"].get("departure_date_time", ""),
                "base_arrival_date_time": item["stop_date_time"].get("base_arrival_date_time", ""),
                "base_departure_date_time": item["stop_date_time"].get("base_departure_date_time", "")
            }
        }

        result[schedule_type].append(simplified_item)

    for disruption in schedules["disruptions"]:
        simplified_disruption = {
            "id": disruption["id"],
            "application_periods": disruption["application_periods"],
            "impacted_objects": disruption["impacted_objects"]
        }

        if "messages" in disruption:
            simplified_disruption["messages"] = disruption["messages"]

        result["disruptions"].append(simplified_disruption)

    return result

def get_tool_definition(schedule_type: Literal["departures", "arrivals"]) -> Tool:
    """
    Get the Tool definition for station schedule tools (departures or arrivals).

    Args:
        schedule_type: Type of schedule ("departures" or "arrivals")

    Returns:
        Tool: The Tool definition
    """

    return Tool(
        name=SncfTools.GET_DEPARTURES.value if schedule_type == "departures" else SncfTools.GET_ARRIVALS.value,
        description=f"""
        Get {schedule_type} to a station and disruptions that may affect them.

        **WARNING**: if the user tells you a city with multiple stations, you should guess the right station by yourself and not use the city name as a station name. If you don't know the station name, you should use the autocomplete_places tool to get the right station.

        This tool can help you get informations about :
        - The next trains {"leaving" if schedule_type == "departures" else "arriving"} from a station and their {"destination" if schedule_type == "departures" else "origin"}
        - The disruptions affecting a station
        """,
        inputSchema={
            "type": "object",
            "properties": {
                "station": {
                    "type": "string",
                    "description": "Station name if you can gess the exact station name the user wants, or station code (stop_area:code or stop_point:code) after using autocomplete_places tool.",
                },
                "datetime": {
                    "type": "string",
                    "description": f"Optional {schedule_type} time (format: YYYYMMDDThhmmss). By default, it uses the current time.",
                    "default": None
                },
                "data_freshness": {
                    "type": "string",
                    "description": "Data freshness (realtime, base_schedule or adapted_schedule). Adapted schedule to get disruptions information.",
                    "default": "realtime"
                }
            },
            "required": ["station"]
        },
    )
