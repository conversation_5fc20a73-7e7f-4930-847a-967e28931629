"""
Tool for autocompleting place names.
"""

from typing import Any, List, Dict
from mcp.types import Tool

from tools import SncfTools

from utils.navitia import make_navitia_request

async def autocomplete_places(
    query: str,
    searched_type: str = "stop_area",
    count: int = 10
) -> Any:
    """
    Get autocomplete results for places to get stations code.

    Args:
        query: Search query string
        searched_type: Type of places to search for (stop_area, stop_point, etc.)
        count: Number of results to return

    Returns:
        List of places with id and name
    """
    suggestions = await make_navitia_request(f"places?q={query}&type[]={searched_type}&count={count}&data_freshness=realtime")

    if suggestions is None:
        return "Error: No suggestions found"

    # Filter results to only include id and name
    filtered_places = [{"id": place.get("id"), "name": place.get("name")} for place in suggestions["places"]]

    return filtered_places


def get_tool_definition() -> Tool:
    """
    Get the Tool definition for the autocomplete_places tool.

    Returns:
        Tool: The Tool definition
    """
    return Tool(
        name=SncfTools.AUTOCOMPLETE_PLACES.value,
        description="Get autocomplete results for places (cities, stations, etc.) to get stations code. Should only be used if you don't know the station name corresponding to the user's journey/input.",
        inputSchema={
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Query string for autocomplete search",
                },
                "searched_type": {
                    "type": "string",
                    "description": "Types of objects to search for (stop_area, address, administrative_region, poi, stop_point)",
                    "default": "stop_area"
                },
            },
            "required": ["query"]
        },
    )
