"""
Utility functions for formatting HTML output.
"""

import os
from typing import Dict, Any, List

from utils.file_utils import read_file_content
from utils.train_logos import get_train_logo
from utils.logger import get_logger

# Get logger for this module
logger = get_logger(__name__)

# Path to HTML template - use absolute path
TEMPLATE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "templates/train_schedule_table.html")

async def format_sncf_schedule(journeys_data: Dict[str, Any]) -> str:
    """
    Formats SNCF journey data into an HTML table with logos.

    Args:
        journeys_data: Dictionary containing journeys and disruptions data

    Returns:
        HTML formatted table as string
    """
    tbody_content = ""

    for journey in journeys_data["journeys"]:
        # Extract journey information
        # Use high-level fields for times and format them
        try:
            departure_time = journey["departure"][9:11] + ":" + journey["departure"][11:13] if journey.get("departure") else "N/A"
        except (IndexError, TypeError):
            departure_time = "N/A"

        try:
            arrival_time = journey["arrival"][9:11] + ":" + journey["arrival"][11:13] if journey.get("arrival") else "N/A"
        except (IndexError, TypeError):
            arrival_time = "N/A"

        # Keep access to sections for station names
        sections = journey.get("sections", [])
        departure = sections[0].get("from_loc", sections[0].get("from", "N/A")) if sections else "N/A"
        arrival = sections[-1].get("to", "N/A") if sections else "N/A"

        # Format duration (duration is in seconds)
        duration = journey.get("duration", 0)
        minutes, _ = divmod(duration, 60)
        hours, minutes = divmod(minutes, 60)
        duration_formatted = f"{hours}h {minutes:02d}m"

        transfers = journey.get("nb_transfers", 0)

        # Determine the main carrier type for the logo
        # First check network, then commercial_mode
        train_type = "Unknown"
        for section in sections:
            transport_info = section.get("transport_info", {})
            if transport_info:
                # Prefer network over commercial_mode as it's more specific
                network = transport_info.get("network", "")
                if network and isinstance(network, str) and network.strip():
                    train_type = network
                    break

                commercial_mode = transport_info.get("commercial_mode", "")
                if commercial_mode and isinstance(commercial_mode, str) and commercial_mode.strip():
                    train_type = commercial_mode
                    break

        # Get the logo SVG content using our get_train_logo function
        logo_svg_content = get_train_logo(train_type)

        # Format transfers text with badge styling
        if transfers == 0:
            transfers_text = '<span class="direct-badge">Direct</span>'
        else:
            transfers_text = f'<span class="connection-badge">{transfers}</span>'

        # Format the carrier cell with logo only - simplified version with fewer columns
        logo_html = logo_svg_content

        # Default price display is N/A
        price_html = """<div class="price-range">N/A</div>"""

        # Get prices from the transport_info
        for section in sections:
            transport_info = section.get("transport_info", {})
            if transport_info and transport_info.get("prices"):
                # If prices are available in the transport_info, format them
                try:
                    prices_str = transport_info.get("prices", "")
                    if prices_str:
                        # Check if it contains profile information (from main.py get_prices_from_opendata)
                        if ":" in prices_str and "€" in prices_str:
                            # Format from main.py: "profile: min-max€, profile2: min-max€"
                            # Extract the first price range for simplicity
                            price_parts = prices_str.split(",")[0].strip()
                            price_range = price_parts.split(":")[1].strip()

                            if "-" in price_range:
                                # It's a range
                                price_html = f"""<div class="price-from">entre</div>
<div class="price-range">{price_range.split('-')[0]}€ et {price_range.split('-')[1]}€</div>"""
                            else:
                                # Single price (remove € if present)
                                price = price_range.replace("€", "").strip()
                                price_html = f"""<div class="price-from">prix fixe</div>
<div class="price-range">{price} €</div>"""
                        # Simple price format (just a number or range)
                        elif "-" in prices_str:
                            # It's a range
                            price_html = f"""<div class="price-from">entre</div>
<div class="price-range">{prices_str.split('-')[0]}€ et {prices_str.split('-')[1]}€</div>"""
                        else:
                            # Single price (remove € if present)
                            price = prices_str.replace("€", "").strip()
                            price_html = f"""<div class="price-from">prix fixe</div>
<div class="price-range">{price} €</div>"""
                        break
                except Exception as e:
                    logging.error(f"Error formatting prices from transport_info: {e}")

        tbody_content += f"""
<tr class="journey-row">
  <td>
    <div class="journey-time departure">{departure_time}</div>
    <div class="journey-station">{departure}</div>
  </td>
  <td>
    <div class="journey-time arrival">{arrival_time}</div>
    <div class="journey-station">{arrival}</div>
  </td>
  <td class="duration">{duration_formatted}</td>
  <td>{transfers_text}</td>
  <td class="carrier-column">
    {logo_html}
  </td>
  <td class="price-column">
    {price_html}
  </td>
</tr>"""

    # Read HTML template
    try:
        template_html = read_file_content(TEMPLATE_PATH)

        if not template_html:
            logging.error(f"Template file not found or empty: {TEMPLATE_PATH}")
            # Return only tbody content if template is not available
            return tbody_content

        # Get origin and destination for the journey title
        origin = "N/A Origin"
        destination = "N/A Destination"

        if journeys_data.get("journeys") and len(journeys_data["journeys"]) > 0:
            # Try to extract from the first journey
            first_journey = journeys_data["journeys"][0]
            first_journey_sections = first_journey.get("sections", [])

            # Find the first section with from_loc or from
            for section in first_journey_sections:
                from_loc = section.get("from_loc", section.get("from", ""))
                if from_loc:
                    origin = from_loc.split(" (")[0] if "(" in from_loc else from_loc  # Remove city in parentheses
                    break

            # Find the last section with to
            for section in reversed(first_journey_sections):
                to_loc = section.get("to", "")
                if to_loc:
                    destination = to_loc.split(" (")[0] if "(" in to_loc else to_loc  # Remove city in parentheses
                    break

        # Update the journey subtitle
        template_html = template_html.replace('<div class="journey-subtitle">N/A Origin - N/A Destination</div>',
                                             f'<div class="journey-subtitle">{origin} - {destination}</div>')

        # Build complete HTML table
        table_html = template_html.replace("<!-- Les données des trains seront insérées ici -->", tbody_content)

        return table_html

    except Exception as e:
        logger.error(f"Error generating HTML with template: {e}", exc_info=True)
        # In case of error with template, fall back to original method
        return tbody_content
