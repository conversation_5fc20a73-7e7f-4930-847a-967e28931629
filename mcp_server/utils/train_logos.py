"""
Utility functions for train logos.
"""

def get_train_logo(train_type: str) -> str:
    """
    Get the SVG logo for a train type.
    
    Args:
        train_type: Type of train (e.g., "tgv", "ouigo")
        
    Returns:
        SVG logo as string
    """
    train_type = train_type.lower()

    # TGV INOUI logo - correctly displayed
    if "tgv" in train_type or "inoui" in train_type:
        return """<svg class="train-logo" viewBox="0 0 700 314" xmlns="http://www.w3.org/2000/svg" xml:space="preserve"  ><path d="M64.92 787.128h-2.356v.922H68.3v-.922h-2.373v-6.436H64.92zm10.74-2.325v-3.632c-.194-.111-.397-.193-.619-.286-.249-.082-.47-.167-.73-.223-.248-.055-.498-.081-.747-.107-.259-.03-.48-.056-.702-.056-.647 0-1.2.108-1.681.304-.471.23-.896.505-1.228.84a3.55 3.55 0 0 0-.785 1.263c-.167.442-.25.947-.25 1.481 0 .528.083 1.033.277 1.512.194.472.444.895.785 1.23.333.356.758.616 1.229.835.452.197.979.279 1.542.279.665 0 1.228-.082 1.672-.25.425-.166.785-.385 1.07-.701l-.7-.754c-.306.286-.62.48-.98.59a2.828 2.828 0 0 1-1.062.194c-.425 0-.813-.086-1.145-.25a2.31 2.31 0 0 1-.868-.62 2.354 2.354 0 0 1-.564-.947c-.138-.334-.194-.728-.194-1.118 0-.423.056-.81.194-1.177.166-.36.36-.676.62-.925.248-.275.553-.468.895-.616.36-.167.72-.223 1.145-.223.36 0 .702.03 1.007.085.305.082.582.164.812.286v2.065H73v.921zm.813 3.247h1.145l2.152-5.984h.028l2.235 5.984h1.126l-2.909-7.358h-.95l-2.826 7.358zm-4.563-17.372a3.56 3.56 0 0 0 3.556 3.55c1.958 0 3.574-1.595 3.574-3.55v-10.575h4.12v10.575c0 4.223-3.446 7.689-7.694 7.689-4.23 0-7.693-3.466-7.693-7.689v-10.575h4.137zm-9.124 6.85A39.22 39.22 0 0 1 61.779 769v-5.735c.111.083.25.166.37.25.526.276 1.117.479 1.755.479.757 0 1.459-.258 2.05-.673v14.207z" style="fill:#63675c;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="matrix(9.3048 0 0 -9.3048 -573.296 7335.297)"/><path d="M63.904 762.399a2.29 2.29 0 0 1-2.291-2.296 2.267 2.267 0 0 1 2.29-2.296c1.266 0 2.3 1.005 2.3 2.296a2.31 2.31 0 0 1-2.3 2.296zm62.616 4.554a3.56 3.56 0 0 0-3.557-3.55 3.552 3.552 0 0 0-3.546 3.55v10.575h-4.148v-10.575c0-4.222 3.475-7.69 7.694-7.69 4.26 0 7.693 3.468 7.693 7.69v10.575h-4.137zm8.005 8.28c1.284 0 2.318 1.042 2.318 2.295 0 1.292-1.034 2.295-2.318 2.295a2.285 2.285 0 0 1-2.299-2.295 2.31 2.31 0 0 1 2.3-2.295zm1.12-15.13c.636 2.738 1.005 5.597 1.005 8.529v5.734c-.108-.083-.25-.166-.369-.248-.524-.277-1.116-.471-1.756-.471a3.4 3.4 0 0 0-2.02.664v-14.208zM99.22 778.367c5.253 0 9.539-4.277 9.539-9.56 0-5.265-4.286-9.543-9.54-9.543-5.263 0-9.55 4.278-9.55 9.543 0 5.283 4.287 9.56 9.55 9.56zm0-23.778c7.86 0 14.23 6.38 14.23 14.218 0 7.855-6.37 14.236-14.23 14.236-7.843 0-14.215-6.38-14.215-14.236 0-7.837 6.372-14.218 14.215-14.218" style="fill:#930c38;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="matrix(9.3048 0 0 -9.3048 -573.296 7335.297)"/></svg>"""

    # OUIGO logo - correctly displayed
    elif "ouigo" in train_type:
        return """<svg class="train-logo" viewBox="0 0 230 230" xmlns="http://www.w3.org/2000/svg"  ><defs><clipPath id="a" clipPathUnits="userSpaceOnUse"><path d="M120.688 3.172h353.899v365.373H120.688z"/></clipPath></defs><g clip-path="url(#a)" transform="matrix(1.25 0 0 -1.25 -249.606 344.236)"><path d="M0 0c-6.325 0-10.821 5.154-10.821 11.29 0 6.126 4.391 11.181 10.725 11.181 6.339 0 10.825-5.164 10.825-11.299C10.729 5.06 6.337 0 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(292.745 251.588)"/><path d="M0 0c-6.325 0-10.821 5.147-10.821 11.284 0 6.123 4.391 11.172 10.725 11.172 6.339 0 10.825-5.145 10.825-11.292C10.729 5.053 6.337 0 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(292.745 91.71)"/><path d="M0 0c0-50.582-40.979-91.554-91.548-91.554-50.59 0-91.563 40.972-91.563 91.554 0 50.518 40.973 91.539 91.563 91.539C-40.979 91.539 0 50.518 0 0" style="fill:#e3006a;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(383.596 183.05)"/><path d="M0 0v16.799a3.902 3.902 0 0 0 3.932 3.934 3.913 3.913 0 0 0 3.936-3.934V.214c0-5.83 2.906-8.839 7.705-8.839 4.801 0 7.711 2.904 7.711 8.559v16.865a3.904 3.904 0 0 0 3.928 3.934 3.903 3.903 0 0 0 3.932-3.934V.248c0-10.821-6.07-16.123-15.676-16.123C5.879-15.875 0-10.528 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(250.506 179.15)"/><path d="M0 0a3.91 3.91 0 0 0 3.938 3.938A3.906 3.906 0 0 0 7.874 0v-28.49c0-2.2-1.738-3.938-3.936-3.938A3.91 3.91 0 0 0 0-28.49z" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(286.956 195.944)"/><path d="M0 0a4.773 4.773 0 0 0-4.771-4.771A4.768 4.768 0 0 0-9.535 0 4.768 4.768 0 0 0 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(295.663 209.258)"/><path d="M0 0h-9.197a3.426 3.426 0 0 1-3.418-3.429c0-1.892 1.528-3.367 3.418-3.367h5.747v-4.89c-1.817-1.685-4.401-2.363-8.065-2.363-6.322 0-11.123 4.852-11.123 11.38 0 6.085 4.85 11.194 10.567 11.194 3.375 0 5.667-.923 7.82-2.451.561-.406 1.267-.809 2.395-.809a3.878 3.878 0 0 1 3.885 3.878c0 1.522-.873 2.601-1.633 3.162-3.223 2.251-6.8 3.471-12.206 3.471-10.828 0-19.053-8.379-19.053-18.541 0-10.563 7.97-18.433 19.108-18.433 5.531 0 10.201 1.687 13.591 4.326 1.743 1.335 2.106 2.391 2.106 4.696v8.251A3.912 3.912 0 0 1 0 0" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(330.636 184.419)"/><path d="M0 0c-6.333 0-10.819 5.154-10.819 11.288 0 6.128 4.392 11.183 10.725 11.183 6.335 0 10.826-5.154 10.826-11.29C10.732 5.062 6.331 0 0 0m0 29.714c-11.027 0-19.047-8.371-19.047-18.533 0-10.16 7.919-18.43 18.953-18.43 11.02 0 19.04 8.373 19.04 18.537 0 10.16-7.911 18.426-18.946 18.426" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(355.993 170.465)"/><path d="M0 0c-6.34 0-10.826 5.154-10.826 11.288 0 6.128 4.388 11.183 10.725 11.183 6.331 0 10.822-5.154 10.822-11.29C10.721 5.062 6.325 0 0 0m0 29.714c-11.033 0-19.055-8.371-19.055-18.533 0-10.16 7.917-18.43 18.954-18.43 11.027 0 19.041 8.373 19.041 18.537 0 10.16-7.915 18.426-18.94 18.426" style="fill:#fff;fill-opacity:1;fill-rule:nonzero;stroke:none" transform="translate(228.19 170.465)"/></g></svg>"""

    # Default placeholder for other train types
    else:
        return f'<svg class="train-logo" xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24"><rect width="40" height="24" fill="#ccc"/><text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">{train_type[0].upper()}</text></svg>'
