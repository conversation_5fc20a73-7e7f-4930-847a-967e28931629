"""
Utility functions for making requests to the Navitia API.
"""

import httpx
import os
from utils.logger import get_logger

# Get logger for this module
logger = get_logger(__name__)

# Get API key from environment
NAVITIA_API_KEY = os.getenv("NAVITIA_API_KEY")
NAVITIA_BASE_URL = os.getenv("NAVITIA_BASE_URL", "https://api.navitia.io/v1")

# Verify API key is set
if not NAVITIA_API_KEY:
    logger.critical("NAVITIA_API_KEY environment variable is not set")
    raise ValueError("NAVITIA_API_KEY is not set")

logger.debug(f"Navitia API configured with base URL: {NAVITIA_BASE_URL}")

async def make_navitia_request(url: str) -> dict:
    """
    Make a request to the Navitia API with proper error handling.

    Args:
        url: The API endpoint path to request

    Returns:
        JSON response as dictionary or None if the request failed
    """
    headers = {"Authorization": NAVITIA_API_KEY}
    full_url = f"{NAVITIA_BASE_URL}/coverage/sncf{url}"
    logger.debug(f"Making Navitia API request to: {full_url}")

    async with httpx.AsyncClient(base_url=f"{NAVITIA_BASE_URL}/coverage/sncf") as client:
        try:
            response = await client.get(url, headers=headers, timeout=30.0)
            response.raise_for_status()
            logger.debug(f"Navitia API request successful: {response.status_code}")
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Navitia API error: {e.response.status_code} - {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"Navitia request error: {str(e)}", exc_info=True)
            return None