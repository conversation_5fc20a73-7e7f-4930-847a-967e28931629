"""
SNCF MCP server implementation.

This module implements an MCP server that provides tools for accessing SNCF train data
through the Navitia and SNCF OpenData APIs. It supports:

- Station search and autocomplete
- Departure and arrival information
- Journey planning with pricing information
- Train information lookup
- Real-time disruption information
"""

from typing import Any, Optional, Sequence, List, Dict, Union
import os
import json
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import <PERSON>l, TextContent, ImageContent, EmbeddedResource
from dotenv import load_dotenv
from datetime import datetime

# Import centralized logger
from utils.logger import configure_logging, get_logger

# Load environment variables
load_dotenv()

# Configure logging
configure_logging()

# Get logger for this module
logger = get_logger(__name__)

# Import tools
from tools import SncfTools
from tools.autocomplete_places import autocomplete_places
from tools.station_schedules import get_station_schedules
from tools.get_journeys import get_journeys
from tools.get_train_infos import get_train_infos

logger.info("Starting SNCF MCP server")

class SncfServer:
    """
    Server class that provides access to SNCF data through various tools.
    """

    async def autocomplete_places(self, **kwargs) -> Any:
        """Wrapper for autocomplete_places tool"""
        return await autocomplete_places(**kwargs)

    async def get_departures(self, **kwargs) -> Any:
        """Wrapper for get_departures tool"""
        return await get_station_schedules(schedule_type="departures", **kwargs)

    async def get_arrivals(self, **kwargs) -> Any:
        """Wrapper for get_arrivals tool"""
        return await get_station_schedules(schedule_type="arrivals", **kwargs)

    async def get_journeys(self, **kwargs) -> Any:
        """Wrapper for get_journeys tool"""
        return await get_journeys(**kwargs)

    async def get_train_infos(self, **kwargs) -> Any:
        """Wrapper for get_train_infos tool"""
        return await get_train_infos(**kwargs)

async def serve() -> None:
    server = Server("mcp-sncf")
    sncf_server = SncfServer()

    @server.list_tools()
    async def list_tools() -> list[Tool]:
        """List available SNCF tools for the MCP server."""
        # Import tool definitions from each tool file
        from tools.autocomplete_places import get_tool_definition as get_autocomplete_places_tool
        from tools.station_schedules import get_tool_definition as get_station_schedule_tool_definition
        from tools.get_journeys import get_tool_definition as get_journeys_tool
        from tools.get_train_infos import get_tool_definition as get_train_infos_tool

        # Return the list of tools
        return [
            get_autocomplete_places_tool(),
            get_station_schedule_tool_definition(schedule_type="departures"),
            get_station_schedule_tool_definition(schedule_type="arrivals"),
            get_journeys_tool(),
            get_train_infos_tool(),
        ]

    @server.call_tool()
    async def call_tool(
        name: str, arguments: dict
    ) -> Sequence[TextContent | ImageContent | EmbeddedResource]:
        """
        Handle tool calls for SNCF queries.

        Args:
            name: The name of the tool to call
            arguments: The arguments to pass to the tool

        Returns:
            The tool's response as TextContent

        Raises:
            ValueError: If the tool name is invalid or if there's an error processing the query
        """
        try:
            if name not in [tool.value for tool in SncfTools]:
                raise ValueError(f"Invalid tool name: {name}")

            # Use if/elif instead of match/case for compatibility
            if name == SncfTools.AUTOCOMPLETE_PLACES.value:
                result = await sncf_server.autocomplete_places(**arguments)
            elif name == SncfTools.GET_DEPARTURES.value:
                result = await sncf_server.get_departures(**arguments)
            elif name == SncfTools.GET_ARRIVALS.value:
                result = await sncf_server.get_arrivals(**arguments)
            elif name == SncfTools.GET_JOURNEYS.value:
                result = await sncf_server.get_journeys(**arguments)
            elif name == SncfTools.GET_TRAIN_INFOS.value:
                result = await sncf_server.get_train_infos(**arguments)
            else:
                raise ValueError(f"Unknown tool: {name}")

            # Return the result as text content
            return [TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            # Log the error details for debugging
            logger.error(f"Error processing tool call: {str(e)}", exc_info=True)
            raise ValueError(f"Error processing SNCF query: {str(e)}")

    # Create server options and prepare to run
    options = server.create_initialization_options()

    # Use stdio server instead of HTTP for MCP communication
    logger.info("Starting SNCF MCP server with stdio communication")
    async with stdio_server() as (read_stream, write_stream):
        logger.debug("Stdio server initialized, running MCP server")
        await server.run(read_stream, write_stream, options)
    logger.info("SNCF MCP server stopped")

if __name__ == "__main__":
    import asyncio
    asyncio.run(serve())
